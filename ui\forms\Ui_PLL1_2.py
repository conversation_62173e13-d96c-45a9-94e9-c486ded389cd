# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file '.\ui\forms\PLL1_2.ui'
#
# Created by: PyQt5 UI code generator 5.15.10
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets

# Import resources for Qt resource system
from ..resources import PLL1_2_rc


class Ui_PLL1_2(object):
    def setupUi(self, PLL1_2):
        PLL1_2.setObjectName("PLL1_2")
        PLL1_2.resize(2048, 1206)
        self.label = QtWidgets.QLabel(PLL1_2)
        self.label.setGeometry(QtCore.QRect(0, 0, 2041, 1234))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.label.setFont(font)
        self.label.setText("")
        self.label.setPixmap(QtGui.QPixmap(":/pll1_2/PLL1and2.bmp"))
        self.label.setScaledContents(True)
        self.label.setObjectName("label")
        self.comboPLL1WindSize = QtWidgets.QComboBox(PLL1_2)
        self.comboPLL1WindSize.setGeometry(QtCore.QRect(700, 181, 136, 31))
        self.comboPLL1WindSize.setObjectName("comboPLL1WindSize")
        self.spinBoxPLL1DLDCNT = QtWidgets.QSpinBox(PLL1_2)
        self.spinBoxPLL1DLDCNT.setGeometry(QtCore.QRect(701, 267, 140, 28))
        self.spinBoxPLL1DLDCNT.setObjectName("spinBoxPLL1DLDCNT")
        self.PLL2PD = QtWidgets.QCheckBox(PLL1_2)
        self.PLL2PD.setGeometry(QtCore.QRect(501, 190, 31, 41))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.PLL2PD.sizePolicy().hasHeightForWidth())
        self.PLL2PD.setSizePolicy(sizePolicy)
        self.PLL2PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.PLL2PD.setText("")
        self.PLL2PD.setObjectName("PLL2PD")
        self.PLL2WINDSIZE = QtWidgets.QComboBox(PLL1_2)
        self.PLL2WINDSIZE.setGeometry(QtCore.QRect(904, 184, 137, 28))
        self.PLL2WINDSIZE.setObjectName("PLL2WINDSIZE")
        self.FCALEN = QtWidgets.QCheckBox(PLL1_2)
        self.FCALEN.setGeometry(QtCore.QRect(1068, 149, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.FCALEN.sizePolicy().hasHeightForWidth())
        self.FCALEN.setSizePolicy(sizePolicy)
        self.FCALEN.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.FCALEN.setText("")
        self.FCALEN.setObjectName("FCALEN")
        self.spinBoxPLL2DLDCNT = QtWidgets.QSpinBox(PLL1_2)
        self.spinBoxPLL2DLDCNT.setGeometry(QtCore.QRect(905, 267, 139, 32))
        self.spinBoxPLL2DLDCNT.setObjectName("spinBoxPLL2DLDCNT")
        self.PLL1CPState = QtWidgets.QComboBox(PLL1_2)
        self.PLL1CPState.setGeometry(QtCore.QRect(994, 509, 116, 26))
        self.PLL1CPState.setObjectName("PLL1CPState")
        self.RBPLL1DLD = QtWidgets.QCheckBox(PLL1_2)
        self.RBPLL1DLD.setGeometry(QtCore.QRect(1383, 150, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RBPLL1DLD.sizePolicy().hasHeightForWidth())
        self.RBPLL1DLD.setSizePolicy(sizePolicy)
        self.RBPLL1DLD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.RBPLL1DLD.setText("")
        self.RBPLL1DLD.setObjectName("RBPLL1DLD")
        self.PLL1NclkMux = QtWidgets.QComboBox(PLL1_2)
        self.PLL1NclkMux.setGeometry(QtCore.QRect(520, 688, 111, 28))
        self.PLL1NclkMux.setObjectName("PLL1NclkMux")
        self.PLL1NDivider = QtWidgets.QSpinBox(PLL1_2)
        self.PLL1NDivider.setGeometry(QtCore.QRect(731, 677, 101, 30))
        self.PLL1NDivider.setObjectName("PLL1NDivider")
        self.PLL1RDividerSetting = QtWidgets.QSpinBox(PLL1_2)
        self.PLL1RDividerSetting.setGeometry(QtCore.QRect(732, 494, 101, 31))
        self.PLL1RDividerSetting.setObjectName("PLL1RDividerSetting")
        self.PLL1PFDFreq = QtWidgets.QLineEdit(PLL1_2)
        self.PLL1PFDFreq.setGeometry(QtCore.QRect(994, 482, 116, 25))
        self.PLL1PFDFreq.setObjectName("PLL1PFDFreq")
        self.PLL1PFDPolarity = QtWidgets.QComboBox(PLL1_2)
        self.PLL1PFDPolarity.setGeometry(QtCore.QRect(994, 536, 116, 30))
        self.PLL1PFDPolarity.setObjectName("PLL1PFDPolarity")
        self.PLL1CPGain = QtWidgets.QComboBox(PLL1_2)
        self.PLL1CPGain.setGeometry(QtCore.QRect(994, 568, 116, 25))
        self.PLL1CPGain.setObjectName("PLL1CPGain")
        self.ExternalVCXOFreq = QtWidgets.QLineEdit(PLL1_2)
        self.ExternalVCXOFreq.setGeometry(QtCore.QRect(1758, 574, 131, 49))
        self.ExternalVCXOFreq.setObjectName("ExternalVCXOFreq")
        self.VCOPD = QtWidgets.QCheckBox(PLL1_2)
        self.VCOPD.setGeometry(QtCore.QRect(1582, 823, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.VCOPD.sizePolicy().hasHeightForWidth())
        self.VCOPD.setSizePolicy(sizePolicy)
        self.VCOPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.VCOPD.setText("")
        self.VCOPD.setObjectName("VCOPD")
        self.Div2 = QtWidgets.QCheckBox(PLL1_2)
        self.Div2.setGeometry(QtCore.QRect(1562, 1112, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.Div2.sizePolicy().hasHeightForWidth())
        self.Div2.setSizePolicy(sizePolicy)
        self.Div2.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.Div2.setText("")
        self.Div2.setObjectName("Div2")
        self.comboVcoMode = QtWidgets.QComboBox(PLL1_2)
        self.comboVcoMode.setGeometry(QtCore.QRect(1725, 1036, 108, 30))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.comboVcoMode.setFont(font)
        self.comboVcoMode.setObjectName("comboVcoMode")
        self.OSCinPD = QtWidgets.QCheckBox(PLL1_2)
        self.OSCinPD.setGeometry(QtCore.QRect(197, 838, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.OSCinPD.sizePolicy().hasHeightForWidth())
        self.OSCinPD.setSizePolicy(sizePolicy)
        self.OSCinPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.OSCinPD.setText("")
        self.OSCinPD.setObjectName("OSCinPD")
        self.FBMuxEn = QtWidgets.QCheckBox(PLL1_2)
        self.FBMuxEn.setGeometry(QtCore.QRect(153, 923, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.FBMuxEn.sizePolicy().hasHeightForWidth())
        self.FBMuxEn.setSizePolicy(sizePolicy)
        self.FBMuxEn.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.FBMuxEn.setText("")
        self.FBMuxEn.setObjectName("FBMuxEn")
        self.FBMUX = QtWidgets.QComboBox(PLL1_2)
        self.FBMUX.setGeometry(QtCore.QRect(191, 1090, 120, 31))
        self.FBMUX.setObjectName("FBMUX")
        self.PLL2NclkMux = QtWidgets.QComboBox(PLL1_2)
        self.PLL2NclkMux.setGeometry(QtCore.QRect(624, 1064, 131, 28))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(8)
        self.PLL2NclkMux.setFont(font)
        self.PLL2NclkMux.setObjectName("PLL2NclkMux")
        self.PLL2Prescaler = QtWidgets.QComboBox(PLL1_2)
        self.PLL2Prescaler.setGeometry(QtCore.QRect(914, 1143, 114, 41))
        self.PLL2Prescaler.setObjectName("PLL2Prescaler")
        self.PLL2PFDFreq = QtWidgets.QLineEdit(PLL1_2)
        self.PLL2PFDFreq.setGeometry(QtCore.QRect(1020, 893, 116, 25))
        self.PLL2PFDFreq.setObjectName("PLL2PFDFreq")
        self.PLL2CPState = QtWidgets.QComboBox(PLL1_2)
        self.PLL2CPState.setGeometry(QtCore.QRect(1020, 920, 116, 25))
        self.PLL2CPState.setObjectName("PLL2CPState")
        self.PLL2PFDPolarity = QtWidgets.QComboBox(PLL1_2)
        self.PLL2PFDPolarity.setGeometry(QtCore.QRect(1020, 948, 116, 29))
        self.PLL2PFDPolarity.setObjectName("PLL2PFDPolarity")
        self.PLL2CPGain = QtWidgets.QComboBox(PLL1_2)
        self.PLL2CPGain.setGeometry(QtCore.QRect(1020, 978, 116, 26))
        self.PLL2CPGain.setObjectName("PLL2CPGain")
        self.PLL2C1 = QtWidgets.QComboBox(PLL1_2)
        self.PLL2C1.setGeometry(QtCore.QRect(1341, 896, 106, 26))
        self.PLL2C1.setObjectName("PLL2C1")
        self.PLL2R3 = QtWidgets.QComboBox(PLL1_2)
        self.PLL2R3.setGeometry(QtCore.QRect(1341, 929, 106, 26))
        self.PLL2R3.setObjectName("PLL2R3")
        self.PLL2C3 = QtWidgets.QComboBox(PLL1_2)
        self.PLL2C3.setGeometry(QtCore.QRect(1341, 960, 106, 26))
        self.PLL2C3.setObjectName("PLL2C3")
        self.Fin0Freq = QtWidgets.QLineEdit(PLL1_2)
        self.Fin0Freq.setGeometry(QtCore.QRect(1126, 1048, 97, 28))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtG")
        font.setPointSize(8)
        self.Fin0Freq.setFont(font)
        self.Fin0Freq.setObjectName("Fin0Freq")
        self.OSCinFreq = QtWidgets.QLineEdit(PLL1_2)
        self.OSCinFreq.setGeometry(QtCore.QRect(197, 796, 113, 30))
        self.OSCinFreq.setObjectName("OSCinFreq")
        self.Doubler = QtWidgets.QComboBox(PLL1_2)
        self.Doubler.setGeometry(QtCore.QRect(471, 815, 81, 32))
        self.Doubler.setObjectName("Doubler")
        self.PLL2RDivider = QtWidgets.QSpinBox(PLL1_2)
        self.PLL2RDivider.setGeometry(QtCore.QRect(850, 882, 95, 29))
        self.PLL2RDivider.setObjectName("PLL2RDivider")
        self.PLL2NDivider = QtWidgets.QSpinBox(PLL1_2)
        self.PLL2NDivider.setGeometry(QtCore.QRect(848, 992, 97, 29))
        self.PLL2NDivider.setObjectName("PLL2NDivider")
        self.OSCin_FREQ = QtWidgets.QComboBox(PLL1_2)
        self.OSCin_FREQ.setGeometry(QtCore.QRect(1062, 280, 107, 31))
        self.OSCin_FREQ.setObjectName("OSCin_FREQ")
        self.RBPLL2DLD = QtWidgets.QCheckBox(PLL1_2)
        self.RBPLL2DLD.setGeometry(QtCore.QRect(1383, 192, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RBPLL2DLD.sizePolicy().hasHeightForWidth())
        self.RBPLL2DLD.setSizePolicy(sizePolicy)
        self.RBPLL2DLD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.RBPLL2DLD.setText("")
        self.RBPLL2DLD.setObjectName("RBPLL2DLD")
        self.RBFcalCAPMODE = QtWidgets.QLineEdit(PLL1_2)
        self.RBFcalCAPMODE.setGeometry(QtCore.QRect(1371, 235, 132, 31))
        self.RBFcalCAPMODE.setObjectName("RBFcalCAPMODE")
        self.RBPLL2VtuneADC = QtWidgets.QLineEdit(PLL1_2)
        self.RBPLL2VtuneADC.setGeometry(QtCore.QRect(1371, 281, 132, 28))
        self.RBPLL2VtuneADC.setObjectName("RBPLL2VtuneADC")
        self.PLL2RclkMux = QtWidgets.QComboBox(PLL1_2)
        self.PLL2RclkMux.setGeometry(QtCore.QRect(634, 884, 131, 28))
        self.PLL2RclkMux.setObjectName("PLL2RclkMux")
        self.PLL2PrePD = QtWidgets.QCheckBox(PLL1_2)
        self.PLL2PrePD.setGeometry(QtCore.QRect(1046, 1136, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.PLL2PrePD.sizePolicy().hasHeightForWidth())
        self.PLL2PrePD.setSizePolicy(sizePolicy)
        self.PLL2PrePD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.PLL2PrePD.setText("")
        self.PLL2PrePD.setObjectName("PLL2PrePD")
        self.Fin0InputType = QtWidgets.QComboBox(PLL1_2)
        self.Fin0InputType.setGeometry(QtCore.QRect(1130, 1080, 139, 28))
        self.Fin0InputType.setObjectName("Fin0InputType")
        self.Fin0PD = QtWidgets.QCheckBox(PLL1_2)
        self.Fin0PD.setGeometry(QtCore.QRect(1310, 1110, 30, 30))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.Fin0PD.sizePolicy().hasHeightForWidth())
        self.Fin0PD.setSizePolicy(sizePolicy)
        self.Fin0PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 22px;  /* 设置方框宽度 */\n"
"                height: 22px; /* 设置方框高度 */\n"
"            }")
        self.Fin0PD.setText("")
        self.Fin0PD.setObjectName("Fin0PD")
        self.FreFin = QtWidgets.QLineEdit(PLL1_2)
        self.FreFin.setGeometry(QtCore.QRect(435, 460, 101, 32))
        self.FreFin.setObjectName("FreFin")
        self.AD_TSENSOR_OUT = QtWidgets.QLineEdit(PLL1_2)
        self.AD_TSENSOR_OUT.setGeometry(QtCore.QRect(1737, 182, 152, 32))
        self.AD_TSENSOR_OUT.setObjectName("AD_TSENSOR_OUT")
        self.SDIO_RDBK_TYPE = QtWidgets.QLineEdit(PLL1_2)
        self.SDIO_RDBK_TYPE.setGeometry(QtCore.QRect(208, 271, 81, 29))
        self.SDIO_RDBK_TYPE.setObjectName("SDIO_RDBK_TYPE")
        self.powerDown = QtWidgets.QCheckBox(PLL1_2)
        self.powerDown.setGeometry(QtCore.QRect(208, 185, 31, 41))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.powerDown.sizePolicy().hasHeightForWidth())
        self.powerDown.setSizePolicy(sizePolicy)
        self.powerDown.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.powerDown.setText("")
        self.powerDown.setObjectName("powerDown")
        self.VCOLdoPD = QtWidgets.QCheckBox(PLL1_2)
        self.VCOLdoPD.setGeometry(QtCore.QRect(501, 232, 31, 41))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.VCOLdoPD.sizePolicy().hasHeightForWidth())
        self.VCOLdoPD.setSizePolicy(sizePolicy)
        self.VCOLdoPD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.VCOLdoPD.setText("")
        self.VCOLdoPD.setObjectName("VCOLdoPD")
        self.PLL2OpenLoop = QtWidgets.QCheckBox(PLL1_2)
        self.PLL2OpenLoop.setGeometry(QtCore.QRect(501, 273, 31, 41))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.PLL2OpenLoop.sizePolicy().hasHeightForWidth())
        self.PLL2OpenLoop.setSizePolicy(sizePolicy)
        self.PLL2OpenLoop.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.PLL2OpenLoop.setText("")
        self.PLL2OpenLoop.setObjectName("PLL2OpenLoop")
        self.PLL1PD = QtWidgets.QCheckBox(PLL1_2)
        self.PLL1PD.setGeometry(QtCore.QRect(501, 142, 31, 41))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.PLL1PD.sizePolicy().hasHeightForWidth())
        self.PLL1PD.setSizePolicy(sizePolicy)
        self.PLL1PD.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.PLL1PD.setText("")
        self.PLL1PD.setObjectName("PLL1PD")
        self.spiReset = QtWidgets.QCheckBox(PLL1_2)
        self.spiReset.setGeometry(QtCore.QRect(208, 141, 31, 41))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spiReset.sizePolicy().hasHeightForWidth())
        self.spiReset.setSizePolicy(sizePolicy)
        self.spiReset.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.spiReset.setText("")
        self.spiReset.setObjectName("spiReset")
        self.SPI3WrireDis = QtWidgets.QCheckBox(PLL1_2)
        self.SPI3WrireDis.setGeometry(QtCore.QRect(208, 229, 31, 41))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SPI3WrireDis.sizePolicy().hasHeightForWidth())
        self.SPI3WrireDis.setSizePolicy(sizePolicy)
        self.SPI3WrireDis.setStyleSheet("            QCheckBox::indicator {\n"
"                width: 27px;  /* 设置方框宽度 */\n"
"                height: 27px; /* 设置方框高度 */\n"
"            }")
        self.SPI3WrireDis.setText("")
        self.SPI3WrireDis.setObjectName("SPI3WrireDis")
        self.VCODistFreq = QtWidgets.QLineEdit(PLL1_2)
        self.VCODistFreq.setGeometry(QtCore.QRect(1844, 918, 119, 41))
        self.VCODistFreq.setObjectName("VCODistFreq")
        self.label_2 = QtWidgets.QLabel(PLL1_2)
        self.label_2.setGeometry(QtCore.QRect(1861, 880, 161, 41))
        font = QtGui.QFont()
        font.setFamily("Agency FB")
        font.setPointSize(12)
        self.label_2.setFont(font)
        self.label_2.setObjectName("label_2")
        self.FCALM1 = QtWidgets.QSpinBox(PLL1_2)
        self.FCALM1.setGeometry(QtCore.QRect(1062, 190, 107, 32))
        self.FCALM1.setObjectName("FCALM1")
        self.FCALM2 = QtWidgets.QSpinBox(PLL1_2)
        self.FCALM2.setGeometry(QtCore.QRect(1062, 234, 107, 32))
        self.FCALM2.setObjectName("FCALM2")
        self.PLL2Cin = QtWidgets.QLineEdit(PLL1_2)
        self.PLL2Cin.setGeometry(QtCore.QRect(290, 1025, 121, 30))
        self.PLL2Cin.setObjectName("PLL2Cin")
        self.label_3 = QtWidgets.QLabel(PLL1_2)
        self.label_3.setGeometry(QtCore.QRect(1964, 930, 54, 21))
        font = QtGui.QFont()
        font.setFamily("SimSun-ExtB")
        font.setPointSize(11)
        self.label_3.setFont(font)
        self.label_3.setObjectName("label_3")

        self.retranslateUi(PLL1_2)
        QtCore.QMetaObject.connectSlotsByName(PLL1_2)

    def retranslateUi(self, PLL1_2):
        _translate = QtCore.QCoreApplication.translate
        self.label_2.setText(_translate("PLL1_2", "VCO Dist Freq"))
        self.label_3.setText(_translate("PLL1_2", "MHz"))
